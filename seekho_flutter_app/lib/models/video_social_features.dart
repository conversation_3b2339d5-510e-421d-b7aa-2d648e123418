import 'dart:convert';

/// Video sharing request model
class VideoShareRequest {
  final String videoId;
  final String platform;
  final String? message;
  final Map<String, dynamic>? metadata;

  VideoShareRequest({
    required this.videoId,
    required this.platform,
    this.message,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'videoId': videoId,
      'platform': platform,
    };
    
    if (message != null) data['message'] = message;
    if (metadata != null) data['metadata'] = metadata;
    
    return data;
  }

  String toJsonString() => json.encode(toJson());
}

/// Video sharing response model
class VideoShareResponse {
  final bool success;
  final String message;
  final VideoShareData? data;

  VideoShareResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory VideoShareResponse.fromJson(Map<String, dynamic> json) {
    return VideoShareResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? VideoShareData.fromJson(json['data']) : null,
    );
  }
}

class VideoShareData {
  final String shareUrl;
  final String shareId;
  final int shareCount;

  VideoShareData({
    required this.shareUrl,
    required this.shareId,
    required this.shareCount,
  });

  factory VideoShareData.fromJson(Map<String, dynamic> json) {
    return VideoShareData(
      shareUrl: json['shareUrl'] ?? '',
      shareId: json['shareId'] ?? '',
      shareCount: json['shareCount'] ?? 0,
    );
  }
}

/// Video comment model
class VideoComment {
  final String id;
  final String videoId;
  final String userId;
  final String userName;
  final String? userAvatar;
  final String content;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int likes;
  final bool isLikedByUser;
  final List<VideoComment> replies;

  VideoComment({
    required this.id,
    required this.videoId,
    required this.userId,
    required this.userName,
    this.userAvatar,
    required this.content,
    required this.createdAt,
    this.updatedAt,
    required this.likes,
    required this.isLikedByUser,
    required this.replies,
  });

  factory VideoComment.fromJson(Map<String, dynamic> json) {
    final List<VideoComment> replies = [];
    if (json['replies'] != null) {
      final repliesList = json['replies'] as List;
      replies.addAll(repliesList.map((item) => VideoComment.fromJson(item)));
    }

    return VideoComment(
      id: json['id'] ?? '',
      videoId: json['videoId'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      userAvatar: json['userAvatar'],
      content: json['content'] ?? '',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      likes: json['likes'] ?? 0,
      isLikedByUser: json['isLikedByUser'] ?? false,
      replies: replies,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'videoId': videoId,
      'userId': userId,
      'userName': userName,
      'userAvatar': userAvatar,
      'content': content,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'likes': likes,
      'isLikedByUser': isLikedByUser,
      'replies': replies.map((reply) => reply.toJson()).toList(),
    };
  }
}

/// Video comments response model
class VideoCommentsResponse {
  final bool success;
  final String message;
  final List<VideoComment> comments;
  final int totalCount;
  final bool hasMore;

  VideoCommentsResponse({
    required this.success,
    required this.message,
    required this.comments,
    required this.totalCount,
    required this.hasMore,
  });

  factory VideoCommentsResponse.fromJson(Map<String, dynamic> json) {
    final List<VideoComment> comments = [];
    if (json['comments'] != null) {
      final commentsList = json['comments'] as List;
      comments.addAll(commentsList.map((item) => VideoComment.fromJson(item)));
    }

    return VideoCommentsResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      comments: comments,
      totalCount: json['totalCount'] ?? 0,
      hasMore: json['hasMore'] ?? false,
    );
  }
}

/// Video favorite/bookmark response model
class VideoActionResponse {
  final bool success;
  final String message;
  final VideoActionData? data;

  VideoActionResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory VideoActionResponse.fromJson(Map<String, dynamic> json) {
    return VideoActionResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? VideoActionData.fromJson(json['data']) : null,
    );
  }
}

class VideoActionData {
  final bool isFavorited;
  final bool isBookmarked;
  final int favoriteCount;
  final int bookmarkCount;

  VideoActionData({
    required this.isFavorited,
    required this.isBookmarked,
    required this.favoriteCount,
    required this.bookmarkCount,
  });

  factory VideoActionData.fromJson(Map<String, dynamic> json) {
    return VideoActionData(
      isFavorited: json['isFavorited'] ?? false,
      isBookmarked: json['isBookmarked'] ?? false,
      favoriteCount: json['favoriteCount'] ?? 0,
      bookmarkCount: json['bookmarkCount'] ?? 0,
    );
  }
}

/// Comment creation request model
class CommentCreateRequest {
  final String videoId;
  final String content;
  final String? parentCommentId;

  CommentCreateRequest({
    required this.videoId,
    required this.content,
    this.parentCommentId,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'videoId': videoId,
      'content': content,
    };
    
    if (parentCommentId != null) data['parentCommentId'] = parentCommentId;
    
    return data;
  }

  String toJsonString() => json.encode(toJson());
}

/// Comment update request model
class CommentUpdateRequest {
  final String commentId;
  final String content;

  CommentUpdateRequest({
    required this.commentId,
    required this.content,
  });

  Map<String, dynamic> toJson() {
    return {
      'commentId': commentId,
      'content': content,
    };
  }

  String toJsonString() => json.encode(toJson());
}

/// Social platforms for sharing
class SocialPlatform {
  static const String facebook = 'facebook';
  static const String twitter = 'twitter';
  static const String whatsapp = 'whatsapp';
  static const String telegram = 'telegram';
  static const String email = 'email';
  static const String copyLink = 'copy_link';
  static const String native = 'native';
}

/// Helper class for social features formatting
class SocialFeaturesFormatter {
  /// Format comment timestamp
  static String formatCommentTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '${weeks}w ago';
    } else {
      final months = (difference.inDays / 30).floor();
      return '${months}mo ago';
    }
  }

  /// Format like count
  static String formatLikeCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      final k = count / 1000;
      return k % 1 == 0 ? '${k.toInt()}K' : '${k.toStringAsFixed(1)}K';
    } else {
      final m = count / 1000000;
      return m % 1 == 0 ? '${m.toInt()}M' : '${m.toStringAsFixed(1)}M';
    }
  }

  /// Get platform display name
  static String getPlatformDisplayName(String platform) {
    switch (platform) {
      case SocialPlatform.facebook:
        return 'Facebook';
      case SocialPlatform.twitter:
        return 'Twitter';
      case SocialPlatform.whatsapp:
        return 'WhatsApp';
      case SocialPlatform.telegram:
        return 'Telegram';
      case SocialPlatform.email:
        return 'Email';
      case SocialPlatform.copyLink:
        return 'Copy Link';
      case SocialPlatform.native:
        return 'Share';
      default:
        return platform;
    }
  }

  /// Get platform icon name (for use with icon fonts or assets)
  static String getPlatformIcon(String platform) {
    switch (platform) {
      case SocialPlatform.facebook:
        return 'facebook';
      case SocialPlatform.twitter:
        return 'twitter';
      case SocialPlatform.whatsapp:
        return 'whatsapp';
      case SocialPlatform.telegram:
        return 'telegram';
      case SocialPlatform.email:
        return 'email';
      case SocialPlatform.copyLink:
        return 'link';
      case SocialPlatform.native:
        return 'share';
      default:
        return 'share';
    }
  }
}
