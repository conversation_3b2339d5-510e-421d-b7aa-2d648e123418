import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/enhanced_user_stats.dart';
import '../shared/utils/constants.dart';
import 'http_client_service.dart';
import 'auth_service.dart';

/// Enhanced user statistics service that integrates with the new backend APIs
class EnhancedUserStatsService {
  
  /// Update user statistics based on activity
  static Future<UserStatsUpdateData?> updateUserStats({
    required String activityType,
    String? contentId,
    String? contentType,
    int? timeSpent,
    double? score,
  }) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for stats update');
        return null;
      }

      final request = UserStatsUpdateRequest(
        activityType: activityType,
        contentId: contentId,
        contentType: contentType,
        timeSpent: timeSpent,
        score: score,
      );

      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.userStatsUpdate}'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: request.toJsonString(),
      );

      if (response.statusCode == 200) {
        final statsResponse = UserStatsUpdateResponse.fromJson(
          json.decode(response.body),
        );
        
        if (statsResponse.success && statsResponse.data != null) {
          debugPrint('✅ User stats updated successfully');
          
          // Log new achievements if any
          if (statsResponse.data!.newAchievements.isNotEmpty) {
            debugPrint('🏆 New achievements unlocked: ${statsResponse.data!.newAchievements.length}');
            for (final achievement in statsResponse.data!.newAchievements) {
              debugPrint('🏆 Achievement: ${achievement.title}');
            }
          }
          
          return statsResponse.data;
        } else {
          debugPrint('❌ User stats update failed: ${statsResponse.message}');
          return null;
        }
      } else {
        debugPrint('❌ User stats update HTTP error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error updating user stats: $e');
      return null;
    }
  }

  /// Get detailed user statistics
  static Future<DetailedUserStats?> getDetailedUserStats() async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for detailed stats');
        return null;
      }

      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.userStatsDetailed}'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final statsResponse = DetailedUserStatsResponse.fromJson(
          json.decode(response.body),
        );
        
        if (statsResponse.success && statsResponse.data != null) {
          debugPrint('✅ Detailed user stats retrieved successfully');
          return statsResponse.data;
        } else {
          debugPrint('❌ Detailed user stats retrieval failed');
          return null;
        }
      } else {
        debugPrint('❌ Detailed user stats HTTP error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error getting detailed user stats: $e');
      return null;
    }
  }

  /// Update stats for video watching activity
  static Future<UserStatsUpdateData?> recordVideoWatched({
    required String videoId,
    required int timeSpent,
  }) async {
    return await updateUserStats(
      activityType: ActivityType.videoWatched,
      contentId: videoId,
      contentType: 'video',
      timeSpent: timeSpent,
    );
  }

  /// Update stats for content completion
  static Future<UserStatsUpdateData?> recordContentCompleted({
    required String contentId,
    required String contentType,
    int? timeSpent,
    double? score,
  }) async {
    return await updateUserStats(
      activityType: ActivityType.contentCompleted,
      contentId: contentId,
      contentType: contentType,
      timeSpent: timeSpent,
      score: score,
    );
  }

  /// Update stats for test passing
  static Future<UserStatsUpdateData?> recordTestPassed({
    required String testId,
    required String testType,
    required double score,
    int? timeSpent,
  }) async {
    return await updateUserStats(
      activityType: ActivityType.testPassed,
      contentId: testId,
      contentType: testType,
      timeSpent: timeSpent,
      score: score,
    );
  }

  /// Update stats for login activity
  static Future<UserStatsUpdateData?> recordLogin() async {
    return await updateUserStats(
      activityType: ActivityType.login,
    );
  }

  /// Update stats for module completion
  static Future<UserStatsUpdateData?> recordModuleCompleted({
    required String moduleId,
    int? timeSpent,
  }) async {
    return await updateUserStats(
      activityType: ActivityType.moduleCompleted,
      contentId: moduleId,
      contentType: 'module',
      timeSpent: timeSpent,
    );
  }

  /// Update stats for streak maintenance
  static Future<UserStatsUpdateData?> recordStreakMaintained() async {
    return await updateUserStats(
      activityType: ActivityType.streakMaintained,
    );
  }

  /// Get user stats summary for quick display
  static Future<Map<String, dynamic>?> getUserStatsSummary() async {
    final detailedStats = await getDetailedUserStats();
    if (detailedStats == null) return null;

    return {
      'videosWatched': detailedStats.videosWatched,
      'totalWatchTime': detailedStats.totalWatchTime,
      'completedCourses': detailedStats.completedCourses,
      'currentStreak': detailedStats.currentStreak,
      'averageProgress': detailedStats.averageProgress,
      'totalBookmarks': detailedStats.totalBookmarks,
      'favoriteVideos': detailedStats.favoriteVideos,
      'recentAchievements': detailedStats.achievements.take(3).toList(),
    };
  }

  /// Check if user has specific achievement
  static Future<bool> hasAchievement(String achievementId) async {
    final detailedStats = await getDetailedUserStats();
    if (detailedStats == null) return false;

    return detailedStats.achievements.any((achievement) => achievement.id == achievementId);
  }

  /// Get user's recent activity
  static Future<List<RecentActivity>?> getRecentActivity({int limit = 10}) async {
    final detailedStats = await getDetailedUserStats();
    if (detailedStats == null) return null;

    return detailedStats.recentActivity.take(limit).toList();
  }

  /// Get module progress statistics
  static Future<Map<String, ModuleProgressStats>?> getModuleProgressStats() async {
    final detailedStats = await getDetailedUserStats();
    if (detailedStats == null) return null;

    return detailedStats.progressByModule;
  }

  /// Calculate learning velocity (content completed per day)
  static Future<double?> getLearningVelocity() async {
    final detailedStats = await getDetailedUserStats();
    if (detailedStats == null) return null;

    // This is a simplified calculation
    // In a real implementation, you'd need more detailed time-based data
    final totalCompleted = detailedStats.progressByModule.values
        .fold(0, (sum, module) => sum + module.completedContent);
    
    // Assuming user has been active for at least 1 day
    // This should be calculated based on actual user registration date
    const assumedActiveDays = 30;
    
    return totalCompleted / assumedActiveDays;
  }

  /// Get achievement progress (how close to next achievement)
  static Future<Map<String, dynamic>?> getAchievementProgress() async {
    final detailedStats = await getDetailedUserStats();
    if (detailedStats == null) return null;

    // This would typically come from the backend
    // For now, return some calculated progress based on current stats
    return {
      'videosWatched': {
        'current': detailedStats.videosWatched,
        'nextMilestone': _getNextMilestone(detailedStats.videosWatched, [10, 25, 50, 100, 250, 500]),
        'progress': _calculateMilestoneProgress(detailedStats.videosWatched, [10, 25, 50, 100, 250, 500]),
      },
      'currentStreak': {
        'current': detailedStats.currentStreak,
        'nextMilestone': _getNextMilestone(detailedStats.currentStreak, [7, 14, 30, 60, 100]),
        'progress': _calculateMilestoneProgress(detailedStats.currentStreak, [7, 14, 30, 60, 100]),
      },
      'completedCourses': {
        'current': detailedStats.completedCourses,
        'nextMilestone': _getNextMilestone(detailedStats.completedCourses, [1, 3, 5, 10, 20]),
        'progress': _calculateMilestoneProgress(detailedStats.completedCourses, [1, 3, 5, 10, 20]),
      },
    };
  }

  /// Helper method to get next milestone
  static int _getNextMilestone(int current, List<int> milestones) {
    for (final milestone in milestones) {
      if (current < milestone) {
        return milestone;
      }
    }
    return milestones.last * 2; // Return double the last milestone if all are achieved
  }

  /// Helper method to calculate progress towards next milestone
  static double _calculateMilestoneProgress(int current, List<int> milestones) {
    int previousMilestone = 0;
    
    for (final milestone in milestones) {
      if (current < milestone) {
        final range = milestone - previousMilestone;
        final progress = current - previousMilestone;
        return progress / range;
      }
      previousMilestone = milestone;
    }
    
    return 1.0; // 100% if all milestones achieved
  }
}
