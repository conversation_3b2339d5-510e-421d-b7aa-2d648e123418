import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/enhanced_progress.dart';
import '../models/content_progress.dart';
import '../shared/utils/constants.dart';
import '../shared/services/http_client_service.dart';
import 'auth_service.dart';

/// Enhanced progress service that integrates with the new backend APIs
class EnhancedProgressService {
  
  /// Record enhanced progress with metadata to the backend
  static Future<bool> recordProgress({
    required String contentId,
    required ContentType contentType,
    required double progressPercentage,
    required int timeSpent,
    required ProgressStatus status,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for progress recording');
        return false;
      }

      final request = EnhancedProgressRequest(
        contentId: contentId,
        contentType: _contentTypeToString(contentType),
        progressPercentage: progressPercentage,
        timeSpent: timeSpent,
        status: _statusToString(status),
        metadata: metadata,
      );

      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.progressRecord}'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: request.toJsonString(),
      );

      if (response.statusCode == 200) {
        final progressResponse = EnhancedProgressResponse.fromJson(
          json.decode(response.body),
        );
        
        if (progressResponse.success) {
          debugPrint('✅ Progress recorded successfully: ${progressResponse.data?.progressId}');
          return true;
        } else {
          debugPrint('❌ Progress recording failed: ${progressResponse.message}');
          return false;
        }
      } else {
        debugPrint('❌ Progress recording HTTP error: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error recording progress: $e');
      return false;
    }
  }

  /// Get bulk progress for multiple content items
  static Future<Map<String, BulkProgressItem>?> getBulkProgress({
    required List<String> contentIds,
    String? moduleId,
  }) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for bulk progress');
        return null;
      }

      final queryParams = <String, String>{
        'contentIds': contentIds.join(','),
      };
      
      if (moduleId != null) {
        queryParams['moduleId'] = moduleId;
      }

      final uri = Uri.parse('${ApiConstants.baseUrl}${ApiConstants.progressBulk}')
          .replace(queryParameters: queryParams);

      final response = await HttpClientService.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final bulkResponse = BulkProgressResponse.fromJson(
          json.decode(response.body),
        );
        
        if (bulkResponse.success) {
          debugPrint('✅ Bulk progress retrieved: ${bulkResponse.data.length} items');
          return bulkResponse.data;
        } else {
          debugPrint('❌ Bulk progress retrieval failed');
          return null;
        }
      } else {
        debugPrint('❌ Bulk progress HTTP error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error getting bulk progress: $e');
      return null;
    }
  }

  /// Get user's overall progress summary
  static Future<UserProgressSummary?> getUserProgressSummary() async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for user progress summary');
        return null;
      }

      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.progressUser}'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final summaryResponse = UserProgressSummaryResponse.fromJson(
          json.decode(response.body),
        );
        
        if (summaryResponse.success && summaryResponse.data != null) {
          debugPrint('✅ User progress summary retrieved');
          return summaryResponse.data;
        } else {
          debugPrint('❌ User progress summary retrieval failed');
          return null;
        }
      } else {
        debugPrint('❌ User progress summary HTTP error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error getting user progress summary: $e');
      return null;
    }
  }

  /// Get progress for a specific module
  static Future<ModuleProgressData?> getModuleProgress(String moduleId) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for module progress');
        return null;
      }

      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.progressModule}/$moduleId'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final moduleResponse = ModuleProgressResponse.fromJson(
          json.decode(response.body),
        );
        
        if (moduleResponse.success && moduleResponse.data != null) {
          debugPrint('✅ Module progress retrieved for: $moduleId');
          return moduleResponse.data;
        } else {
          debugPrint('❌ Module progress retrieval failed for: $moduleId');
          return null;
        }
      } else {
        debugPrint('❌ Module progress HTTP error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error getting module progress: $e');
      return null;
    }
  }

  /// Convert ContentType enum to string
  static String _contentTypeToString(ContentType type) {
    switch (type) {
      case ContentType.video:
        return 'video';
      case ContentType.text:
        return 'text';
      case ContentType.mcq:
        return 'mcq';
      case ContentType.questionnaire:
        return 'questionnaire';
      case ContentType.module:
        return 'module';
    }
  }

  /// Convert ProgressStatus enum to string
  static String _statusToString(ProgressStatus status) {
    switch (status) {
      case ProgressStatus.notStarted:
        return 'notStarted';
      case ProgressStatus.inProgress:
        return 'inProgress';
      case ProgressStatus.completed:
        return 'completed';
    }
  }

  /// Convert string to ProgressStatus enum
  static ProgressStatus _stringToStatus(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return ProgressStatus.completed;
      case 'inprogress':
      case 'in_progress':
        return ProgressStatus.inProgress;
      default:
        return ProgressStatus.notStarted;
    }
  }

  /// Convert BulkProgressItem to ContentProgress for compatibility
  static ContentProgress bulkProgressToContentProgress(
    String contentId,
    BulkProgressItem item,
    ContentType contentType,
  ) {
    return ContentProgress(
      contentId: contentId,
      contentType: contentType,
      progressPercentage: item.progressPercentage,
      status: _stringToStatus(item.status),
      timeSpent: item.timeSpent,
      lastAccessed: DateTime.tryParse(item.lastAccessed) ?? DateTime.now(),
      metadata: item.metadata,
    );
  }

  /// Sync local progress with backend (for migration/backup)
  static Future<bool> syncLocalProgressToBackend(
    Map<String, ContentProgress> localProgress,
  ) async {
    try {
      int successCount = 0;
      int totalCount = localProgress.length;

      for (final entry in localProgress.entries) {
        final contentId = entry.key;
        final progress = entry.value;

        final success = await recordProgress(
          contentId: contentId,
          contentType: progress.contentType,
          progressPercentage: progress.progressPercentage,
          timeSpent: progress.timeSpent,
          status: progress.status,
          metadata: progress.metadata,
        );

        if (success) {
          successCount++;
        }

        // Add small delay to avoid overwhelming the server
        await Future.delayed(const Duration(milliseconds: 100));
      }

      debugPrint('✅ Synced $successCount/$totalCount progress items to backend');
      return successCount == totalCount;
    } catch (e) {
      debugPrint('❌ Error syncing local progress to backend: $e');
      return false;
    }
  }
}
