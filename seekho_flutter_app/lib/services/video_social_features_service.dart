import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/video_social_features.dart';
import '../shared/utils/constants.dart';
import 'http_client_service.dart';
import 'auth_service.dart';

/// Video social features service for sharing, commenting, and bookmarking
class VideoSocialFeaturesService {
  
  /// Share a video to a social platform
  static Future<VideoShareData?> shareVideo({
    required String videoId,
    required String platform,
    String? message,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for video sharing');
        return null;
      }

      final request = VideoShareRequest(
        videoId: videoId,
        platform: platform,
        message: message,
        metadata: metadata,
      );

      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videoShare}/$videoId/share'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: request.toJsonString(),
      );

      if (response.statusCode == 200) {
        final shareResponse = VideoShareResponse.fromJson(
          json.decode(response.body),
        );
        
        if (shareResponse.success && shareResponse.data != null) {
          debugPrint('✅ Video shared successfully to $platform');
          return shareResponse.data;
        } else {
          debugPrint('❌ Video sharing failed: ${shareResponse.message}');
          return null;
        }
      } else {
        debugPrint('❌ Video sharing HTTP error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error sharing video: $e');
      return null;
    }
  }

  /// Get comments for a video
  static Future<VideoCommentsResponse?> getVideoComments({
    required String videoId,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for video comments');
        return null;
      }

      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videoComments}/$videoId/comments?page=$page&limit=$limit'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final commentsResponse = VideoCommentsResponse.fromJson(
          json.decode(response.body),
        );
        
        if (commentsResponse.success) {
          debugPrint('✅ Video comments retrieved successfully');
          return commentsResponse;
        } else {
          debugPrint('❌ Video comments retrieval failed: ${commentsResponse.message}');
          return null;
        }
      } else {
        debugPrint('❌ Video comments HTTP error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error getting video comments: $e');
      return null;
    }
  }

  /// Add a comment to a video
  static Future<VideoComment?> addComment({
    required String videoId,
    required String content,
    String? parentCommentId,
  }) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for adding comment');
        return null;
      }

      final request = CommentCreateRequest(
        videoId: videoId,
        content: content,
        parentCommentId: parentCommentId,
      );

      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videoComments}/$videoId/comments'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: request.toJsonString(),
      );

      if (response.statusCode == 201) {
        final responseData = json.decode(response.body);
        if (responseData['success'] == true && responseData['data'] != null) {
          final comment = VideoComment.fromJson(responseData['data']);
          debugPrint('✅ Comment added successfully');
          return comment;
        } else {
          debugPrint('❌ Comment addition failed: ${responseData['message']}');
          return null;
        }
      } else {
        debugPrint('❌ Comment addition HTTP error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error adding comment: $e');
      return null;
    }
  }

  /// Update a comment
  static Future<VideoComment?> updateComment({
    required String commentId,
    required String content,
  }) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for updating comment');
        return null;
      }

      final request = CommentUpdateRequest(
        commentId: commentId,
        content: content,
      );

      final response = await HttpClientService.put(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videoComments}/comments/$commentId'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: request.toJsonString(),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['success'] == true && responseData['data'] != null) {
          final comment = VideoComment.fromJson(responseData['data']);
          debugPrint('✅ Comment updated successfully');
          return comment;
        } else {
          debugPrint('❌ Comment update failed: ${responseData['message']}');
          return null;
        }
      } else {
        debugPrint('❌ Comment update HTTP error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error updating comment: $e');
      return null;
    }
  }

  /// Delete a comment
  static Future<bool> deleteComment(String commentId) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for deleting comment');
        return false;
      }

      final response = await HttpClientService.delete(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videoComments}/comments/$commentId'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['success'] == true) {
          debugPrint('✅ Comment deleted successfully');
          return true;
        } else {
          debugPrint('❌ Comment deletion failed: ${responseData['message']}');
          return false;
        }
      } else {
        debugPrint('❌ Comment deletion HTTP error: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error deleting comment: $e');
      return false;
    }
  }

  /// Like or unlike a comment
  static Future<bool> toggleCommentLike(String commentId) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for liking comment');
        return false;
      }

      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videoComments}/comments/$commentId/like'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: json.encode({}),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['success'] == true) {
          debugPrint('✅ Comment like toggled successfully');
          return true;
        } else {
          debugPrint('❌ Comment like toggle failed: ${responseData['message']}');
          return false;
        }
      } else {
        debugPrint('❌ Comment like toggle HTTP error: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error toggling comment like: $e');
      return false;
    }
  }

  /// Add or remove video from favorites
  static Future<VideoActionData?> toggleVideoFavorite(String videoId) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for favoriting video');
        return null;
      }

      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videoFavorite}/$videoId/favorite'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: json.encode({}),
      );

      if (response.statusCode == 200) {
        final actionResponse = VideoActionResponse.fromJson(
          json.decode(response.body),
        );
        
        if (actionResponse.success && actionResponse.data != null) {
          debugPrint('✅ Video favorite toggled successfully');
          return actionResponse.data;
        } else {
          debugPrint('❌ Video favorite toggle failed: ${actionResponse.message}');
          return null;
        }
      } else {
        debugPrint('❌ Video favorite toggle HTTP error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error toggling video favorite: $e');
      return null;
    }
  }

  /// Add or remove video from bookmarks
  static Future<VideoActionData?> toggleVideoBookmark(String videoId) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        debugPrint('No auth token available for bookmarking video');
        return null;
      }

      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videoBookmark}/$videoId/bookmark'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: json.encode({}),
      );

      if (response.statusCode == 200) {
        final actionResponse = VideoActionResponse.fromJson(
          json.decode(response.body),
        );
        
        if (actionResponse.success && actionResponse.data != null) {
          debugPrint('✅ Video bookmark toggled successfully');
          return actionResponse.data;
        } else {
          debugPrint('❌ Video bookmark toggle failed: ${actionResponse.message}');
          return null;
        }
      } else {
        debugPrint('❌ Video bookmark toggle HTTP error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error toggling video bookmark: $e');
      return null;
    }
  }

  /// Copy video share link to clipboard
  static Future<bool> copyVideoLink(String videoId, String shareUrl) async {
    try {
      await Clipboard.setData(ClipboardData(text: shareUrl));
      debugPrint('✅ Video link copied to clipboard');
      return true;
    } catch (e) {
      debugPrint('❌ Error copying video link: $e');
      return false;
    }
  }

  /// Get available sharing platforms
  static List<String> getAvailablePlatforms() {
    return [
      SocialPlatform.whatsapp,
      SocialPlatform.telegram,
      SocialPlatform.facebook,
      SocialPlatform.twitter,
      SocialPlatform.email,
      SocialPlatform.copyLink,
      SocialPlatform.native,
    ];
  }
}
