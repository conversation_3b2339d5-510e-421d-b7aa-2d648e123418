import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/auth_response.dart';
import '../models/user.dart';
import '../utils/constants.dart';
import 'http_client_service.dart';
import 'enhanced_user_stats_service.dart';

class AuthService {
  static const _storage = FlutterSecureStorage();

  // Get Android configuration from backend
  static Future<AndroidConfigData?> getAndroidConfig() async {
    try {
      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.androidConfig}'),
      );

      if (response.statusCode == 200) {
        final configResponse = AndroidConfigResponse.fromJson(
          json.decode(response.body),
        );
        return configResponse.data;
      }
      return null;
    } catch (e) {
      print('Error fetching Android config: $e');
      return null;
    }
  }

  // Authenticate with Google ID token
  static Future<AuthResponse> authenticateWithGoogle(String idToken) async {
    try {
      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.androidGoogleAuth}'),
        body: json.encode({'idToken': idToken}),
      );

      final authResponse = AuthResponse.fromJson(json.decode(response.body));

      if (authResponse.success && authResponse.data != null) {
        // Store tokens securely
        await _storage.write(
          key: StorageKeys.accessToken,
          value: authResponse.data!.token,
        );

        // Store user info
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(
          StorageKeys.userInfo,
          json.encode(authResponse.data!.user.toJson()),
        );
        await prefs.setBool(StorageKeys.isLoggedIn, true);
      }

      return authResponse;
    } catch (e) {
      print('Error authenticating with Google: $e');
      return AuthResponse(
        success: false,
        message: 'Authentication failed: $e',
      );
    }
  }

  // Authenticate with email and password
  static Future<AuthResponse> authenticateWithEmail(String email, String password) async {
    try {
      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.emailLogin}'),
        body: json.encode({
          'email': email,
          'password': password,
        }),
      );

      final authResponse = AuthResponse.fromJson(json.decode(response.body));

      if (authResponse.success && authResponse.data != null) {
        // Store tokens securely
        await _storage.write(
          key: StorageKeys.accessToken,
          value: authResponse.data!.token,
        );

        // Store user info
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(
          StorageKeys.userInfo,
          json.encode(authResponse.data!.user.toJson()),
        );
        await prefs.setBool(StorageKeys.isLoggedIn, true);
      }

      return authResponse;
    } catch (e) {
      print('Error authenticating with email: $e');
      return AuthResponse(
        success: false,
        message: 'Authentication failed: $e',
      );
    }
  }

  // Get stored access token
  static Future<String?> getAccessToken() async {
    return await _storage.read(key: StorageKeys.accessToken);
  }

  // Save user data locally
  static Future<void> saveUserData(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        StorageKeys.userInfo,
        json.encode(user.toJson()),
      );
      await prefs.setBool(StorageKeys.isLoggedIn, true);

      // Record login activity for user statistics
      EnhancedUserStatsService.recordLogin();
    } catch (e) {
      print('Error saving user data: $e');
      throw Exception('Failed to save user data: $e');
    }
  }

  // Get stored user info
  static Future<User?> getStoredUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(StorageKeys.userInfo);
      if (userJson != null) {
        return User.fromJson(json.decode(userJson));
      }
      return null;
    } catch (e) {
      print('Error getting stored user: $e');
      return null;
    }
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(StorageKeys.isLoggedIn) ?? false;
  }

  // Logout
  static Future<void> logout() async {
    try {
      // Clear secure storage
      await _storage.deleteAll();

      // Clear shared preferences but preserve progress data
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(StorageKeys.userInfo);
      await prefs.setBool(StorageKeys.isLoggedIn, false);

      // Clear all onboarding completion flags
      final keys = prefs.getKeys();
      for (final key in keys) {
        if (key.startsWith('onboarding_complete_')) {
          await prefs.remove(key);
        }
      }

      // Note: We intentionally preserve 'user_progress' data so users don't lose their learning progress
    } catch (e) {
      print('Error during logout: $e');
    }
  }
}
