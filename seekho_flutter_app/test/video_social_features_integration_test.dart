import 'package:flutter_test/flutter_test.dart';
import 'package:bolo_app/models/video_social_features.dart';
import 'package:bolo_app/services/video_social_features_service.dart';

void main() {
  group('Video Social Features Integration Tests', () {
    
    test('VideoShareRequest should serialize correctly', () {
      final request = VideoShareRequest(
        videoId: 'test-video-123',
        platform: SocialPlatform.whatsapp,
        message: 'Check out this amazing video!',
        metadata: {'source': 'mobile_app'},
      );

      final json = request.toJson();
      
      expect(json['videoId'], equals('test-video-123'));
      expect(json['platform'], equals(SocialPlatform.whatsapp));
      expect(json['message'], equals('Check out this amazing video!'));
      expect(json['metadata'], equals({'source': 'mobile_app'}));
    });

    test('VideoShareRequest should handle optional fields correctly', () {
      final minimalRequest = VideoShareRequest(
        videoId: 'test-video-123',
        platform: SocialPlatform.copyLink,
      );

      final json = minimalRequest.toJson();
      
      expect(json['videoId'], equals('test-video-123'));
      expect(json['platform'], equals(SocialPlatform.copyLink));
      expect(json.containsKey('message'), isFalse);
      expect(json.containsKey('metadata'), isFalse);
    });

    test('VideoShareResponse should deserialize correctly', () {
      final jsonResponse = {
        'success': true,
        'message': 'Video shared successfully',
        'data': {
          'shareUrl': 'https://example.com/share/video-123',
          'shareId': 'share-456',
          'shareCount': 42,
        },
      };

      final response = VideoShareResponse.fromJson(jsonResponse);
      
      expect(response.success, isTrue);
      expect(response.message, equals('Video shared successfully'));
      expect(response.data, isNotNull);
      expect(response.data!.shareUrl, equals('https://example.com/share/video-123'));
      expect(response.data!.shareId, equals('share-456'));
      expect(response.data!.shareCount, equals(42));
    });

    test('VideoComment should deserialize correctly with replies', () {
      final jsonComment = {
        'id': 'comment-123',
        'videoId': 'video-456',
        'userId': 'user-789',
        'userName': 'John Doe',
        'userAvatar': 'https://example.com/avatar.jpg',
        'content': 'Great video! Very helpful.',
        'createdAt': '2024-01-15T10:30:00Z',
        'updatedAt': '2024-01-15T10:35:00Z',
        'likes': 15,
        'isLikedByUser': true,
        'replies': [
          {
            'id': 'reply-123',
            'videoId': 'video-456',
            'userId': 'user-999',
            'userName': 'Jane Smith',
            'content': 'I agree!',
            'createdAt': '2024-01-15T11:00:00Z',
            'likes': 3,
            'isLikedByUser': false,
            'replies': [],
          },
        ],
      };

      final comment = VideoComment.fromJson(jsonComment);
      
      expect(comment.id, equals('comment-123'));
      expect(comment.videoId, equals('video-456'));
      expect(comment.userId, equals('user-789'));
      expect(comment.userName, equals('John Doe'));
      expect(comment.userAvatar, equals('https://example.com/avatar.jpg'));
      expect(comment.content, equals('Great video! Very helpful.'));
      expect(comment.likes, equals(15));
      expect(comment.isLikedByUser, isTrue);
      expect(comment.replies.length, equals(1));
      
      final reply = comment.replies.first;
      expect(reply.id, equals('reply-123'));
      expect(reply.userName, equals('Jane Smith'));
      expect(reply.content, equals('I agree!'));
      expect(reply.likes, equals(3));
      expect(reply.isLikedByUser, isFalse);
    });

    test('VideoCommentsResponse should deserialize correctly', () {
      final jsonResponse = {
        'success': true,
        'message': 'Comments retrieved successfully',
        'comments': [
          {
            'id': 'comment-1',
            'videoId': 'video-123',
            'userId': 'user-1',
            'userName': 'User One',
            'content': 'First comment',
            'createdAt': '2024-01-15T10:00:00Z',
            'likes': 5,
            'isLikedByUser': false,
            'replies': [],
          },
          {
            'id': 'comment-2',
            'videoId': 'video-123',
            'userId': 'user-2',
            'userName': 'User Two',
            'content': 'Second comment',
            'createdAt': '2024-01-15T11:00:00Z',
            'likes': 8,
            'isLikedByUser': true,
            'replies': [],
          },
        ],
        'totalCount': 25,
        'hasMore': true,
      };

      final response = VideoCommentsResponse.fromJson(jsonResponse);
      
      expect(response.success, isTrue);
      expect(response.message, equals('Comments retrieved successfully'));
      expect(response.comments.length, equals(2));
      expect(response.totalCount, equals(25));
      expect(response.hasMore, isTrue);
      
      expect(response.comments.first.id, equals('comment-1'));
      expect(response.comments.first.userName, equals('User One'));
      expect(response.comments.last.id, equals('comment-2'));
      expect(response.comments.last.isLikedByUser, isTrue);
    });

    test('VideoActionResponse should deserialize correctly', () {
      final jsonResponse = {
        'success': true,
        'message': 'Video favorited successfully',
        'data': {
          'isFavorited': true,
          'isBookmarked': false,
          'favoriteCount': 127,
          'bookmarkCount': 89,
        },
      };

      final response = VideoActionResponse.fromJson(jsonResponse);
      
      expect(response.success, isTrue);
      expect(response.message, equals('Video favorited successfully'));
      expect(response.data, isNotNull);
      expect(response.data!.isFavorited, isTrue);
      expect(response.data!.isBookmarked, isFalse);
      expect(response.data!.favoriteCount, equals(127));
      expect(response.data!.bookmarkCount, equals(89));
    });

    test('CommentCreateRequest should serialize correctly', () {
      final request = CommentCreateRequest(
        videoId: 'video-123',
        content: 'This is a test comment',
        parentCommentId: 'parent-456',
      );

      final json = request.toJson();
      
      expect(json['videoId'], equals('video-123'));
      expect(json['content'], equals('This is a test comment'));
      expect(json['parentCommentId'], equals('parent-456'));
    });

    test('CommentCreateRequest should handle optional parent comment', () {
      final request = CommentCreateRequest(
        videoId: 'video-123',
        content: 'This is a root comment',
      );

      final json = request.toJson();
      
      expect(json['videoId'], equals('video-123'));
      expect(json['content'], equals('This is a root comment'));
      expect(json.containsKey('parentCommentId'), isFalse);
    });

    test('CommentUpdateRequest should serialize correctly', () {
      final request = CommentUpdateRequest(
        commentId: 'comment-123',
        content: 'Updated comment content',
      );

      final json = request.toJson();
      
      expect(json['commentId'], equals('comment-123'));
      expect(json['content'], equals('Updated comment content'));
    });

    test('SocialFeaturesFormatter should format comment time correctly', () {
      final now = DateTime.now();
      
      // Just now
      expect(SocialFeaturesFormatter.formatCommentTime(now.subtract(const Duration(seconds: 30))), 
             equals('Just now'));
      
      // Minutes ago
      expect(SocialFeaturesFormatter.formatCommentTime(now.subtract(const Duration(minutes: 5))), 
             equals('5m ago'));
      
      // Hours ago
      expect(SocialFeaturesFormatter.formatCommentTime(now.subtract(const Duration(hours: 2))), 
             equals('2h ago'));
      
      // Days ago
      expect(SocialFeaturesFormatter.formatCommentTime(now.subtract(const Duration(days: 3))), 
             equals('3d ago'));
      
      // Weeks ago
      expect(SocialFeaturesFormatter.formatCommentTime(now.subtract(const Duration(days: 14))), 
             equals('2w ago'));
      
      // Months ago
      expect(SocialFeaturesFormatter.formatCommentTime(now.subtract(const Duration(days: 60))), 
             equals('2mo ago'));
    });

    test('SocialFeaturesFormatter should format like count correctly', () {
      expect(SocialFeaturesFormatter.formatLikeCount(42), equals('42'));
      expect(SocialFeaturesFormatter.formatLikeCount(1000), equals('1K'));
      expect(SocialFeaturesFormatter.formatLikeCount(1500), equals('1.5K'));
      expect(SocialFeaturesFormatter.formatLikeCount(1000000), equals('1M'));
      expect(SocialFeaturesFormatter.formatLikeCount(2500000), equals('2.5M'));
    });

    test('SocialFeaturesFormatter should provide platform display names', () {
      expect(SocialFeaturesFormatter.getPlatformDisplayName(SocialPlatform.facebook), 
             equals('Facebook'));
      expect(SocialFeaturesFormatter.getPlatformDisplayName(SocialPlatform.twitter), 
             equals('Twitter'));
      expect(SocialFeaturesFormatter.getPlatformDisplayName(SocialPlatform.whatsapp), 
             equals('WhatsApp'));
      expect(SocialFeaturesFormatter.getPlatformDisplayName(SocialPlatform.telegram), 
             equals('Telegram'));
      expect(SocialFeaturesFormatter.getPlatformDisplayName(SocialPlatform.email), 
             equals('Email'));
      expect(SocialFeaturesFormatter.getPlatformDisplayName(SocialPlatform.copyLink), 
             equals('Copy Link'));
      expect(SocialFeaturesFormatter.getPlatformDisplayName('unknown'), 
             equals('unknown'));
    });

    test('SocialFeaturesFormatter should provide platform icons', () {
      expect(SocialFeaturesFormatter.getPlatformIcon(SocialPlatform.facebook), 
             equals('facebook'));
      expect(SocialFeaturesFormatter.getPlatformIcon(SocialPlatform.twitter), 
             equals('twitter'));
      expect(SocialFeaturesFormatter.getPlatformIcon(SocialPlatform.whatsapp), 
             equals('whatsapp'));
      expect(SocialFeaturesFormatter.getPlatformIcon(SocialPlatform.copyLink), 
             equals('link'));
      expect(SocialFeaturesFormatter.getPlatformIcon('unknown'), 
             equals('share'));
    });

    test('Social platform constants should be defined correctly', () {
      expect(SocialPlatform.facebook, equals('facebook'));
      expect(SocialPlatform.twitter, equals('twitter'));
      expect(SocialPlatform.whatsapp, equals('whatsapp'));
      expect(SocialPlatform.telegram, equals('telegram'));
      expect(SocialPlatform.email, equals('email'));
      expect(SocialPlatform.copyLink, equals('copy_link'));
      expect(SocialPlatform.native, equals('native'));
    });

    test('VideoSocialFeaturesService should provide available platforms', () {
      final platforms = VideoSocialFeaturesService.getAvailablePlatforms();
      
      expect(platforms, isA<List<String>>());
      expect(platforms.contains(SocialPlatform.whatsapp), isTrue);
      expect(platforms.contains(SocialPlatform.telegram), isTrue);
      expect(platforms.contains(SocialPlatform.facebook), isTrue);
      expect(platforms.contains(SocialPlatform.twitter), isTrue);
      expect(platforms.contains(SocialPlatform.email), isTrue);
      expect(platforms.contains(SocialPlatform.copyLink), isTrue);
      expect(platforms.contains(SocialPlatform.native), isTrue);
    });

    test('VideoComment should serialize correctly', () {
      final comment = VideoComment(
        id: 'comment-123',
        videoId: 'video-456',
        userId: 'user-789',
        userName: 'Test User',
        userAvatar: 'https://example.com/avatar.jpg',
        content: 'Test comment content',
        createdAt: DateTime.parse('2024-01-15T10:30:00Z'),
        updatedAt: DateTime.parse('2024-01-15T10:35:00Z'),
        likes: 10,
        isLikedByUser: true,
        replies: [],
      );

      final json = comment.toJson();
      
      expect(json['id'], equals('comment-123'));
      expect(json['videoId'], equals('video-456'));
      expect(json['userId'], equals('user-789'));
      expect(json['userName'], equals('Test User'));
      expect(json['userAvatar'], equals('https://example.com/avatar.jpg'));
      expect(json['content'], equals('Test comment content'));
      expect(json['createdAt'], equals('2024-01-15T10:30:00.000Z'));
      expect(json['updatedAt'], equals('2024-01-15T10:35:00.000Z'));
      expect(json['likes'], equals(10));
      expect(json['isLikedByUser'], isTrue);
      expect(json['replies'], equals([]));
    });
  });
}
