import 'package:flutter_test/flutter_test.dart';
import 'package:bolo_app/services/enhanced_user_stats_service.dart';
import 'package:bolo_app/models/enhanced_user_stats.dart';

void main() {
  group('Enhanced User Statistics Integration Tests', () {
    
    test('UserStatsUpdateRequest should serialize correctly', () {
      final request = UserStatsUpdateRequest(
        activityType: ActivityType.videoWatched,
        contentId: 'test-video-123',
        contentType: 'video',
        timeSpent: 300,
        score: 85.5,
      );

      final json = request.toJson();
      
      expect(json['activityType'], equals(ActivityType.videoWatched));
      expect(json['contentId'], equals('test-video-123'));
      expect(json['contentType'], equals('video'));
      expect(json['timeSpent'], equals(300));
      expect(json['score'], equals(85.5));
    });

    test('UserStatsUpdateResponse should deserialize correctly', () {
      final jsonResponse = {
        'success': true,
        'message': 'Stats updated successfully',
        'data': {
          'currentStreak': 7,
          'lastActivityAt': '2024-01-15T10:30:00Z',
          'newAchievements': [
            {
              'id': 'achievement-1',
              'title': 'Video Watcher',
              'description': 'Watched 10 videos',
              'iconUrl': 'https://example.com/icon.png',
              'unlockedAt': '2024-01-15T10:30:00Z',
            }
          ],
        },
      };

      final response = UserStatsUpdateResponse.fromJson(jsonResponse);
      
      expect(response.success, isTrue);
      expect(response.message, equals('Stats updated successfully'));
      expect(response.data, isNotNull);
      expect(response.data!.currentStreak, equals(7));
      expect(response.data!.newAchievements.length, equals(1));
      expect(response.data!.newAchievements.first.title, equals('Video Watcher'));
    });

    test('DetailedUserStats should deserialize correctly', () {
      final jsonResponse = {
        'success': true,
        'data': {
          'videosWatched': 25,
          'totalWatchTime': 7200,
          'completedCourses': 3,
          'favoriteVideos': 8,
          'totalBookmarks': 12,
          'currentStreak': 14,
          'averageProgress': 78.5,
          'progressByModule': {
            'module-1': {
              'completedContent': 8,
              'totalContent': 10,
              'progressPercentage': 80.0,
            },
            'module-2': {
              'completedContent': 5,
              'totalContent': 8,
              'progressPercentage': 62.5,
            },
          },
          'recentActivity': [
            {
              'type': 'video_watched',
              'contentTitle': 'Introduction to Flutter',
              'timestamp': '2024-01-15T10:30:00Z',
            },
            {
              'type': 'test_passed',
              'contentTitle': 'Flutter Basics Quiz',
              'timestamp': '2024-01-15T09:15:00Z',
            },
          ],
          'achievements': [
            {
              'id': 'achievement-1',
              'title': 'First Steps',
              'description': 'Completed your first lesson',
              'iconUrl': 'https://example.com/first-steps.png',
              'unlockedAt': '2024-01-10T08:00:00Z',
            },
          ],
        },
      };

      final response = DetailedUserStatsResponse.fromJson(jsonResponse);
      
      expect(response.success, isTrue);
      expect(response.data, isNotNull);
      
      final stats = response.data!;
      expect(stats.videosWatched, equals(25));
      expect(stats.totalWatchTime, equals(7200));
      expect(stats.completedCourses, equals(3));
      expect(stats.currentStreak, equals(14));
      expect(stats.averageProgress, equals(78.5));
      
      expect(stats.progressByModule.length, equals(2));
      expect(stats.progressByModule['module-1']!.completedContent, equals(8));
      expect(stats.progressByModule['module-1']!.progressPercentage, equals(80.0));
      
      expect(stats.recentActivity.length, equals(2));
      expect(stats.recentActivity.first.type, equals('video_watched'));
      expect(stats.recentActivity.first.contentTitle, equals('Introduction to Flutter'));
      
      expect(stats.achievements.length, equals(1));
      expect(stats.achievements.first.title, equals('First Steps'));
    });

    test('StatsFormatter should format time correctly', () {
      expect(StatsFormatter.formatWatchTime(45), equals('45s'));
      expect(StatsFormatter.formatWatchTime(90), equals('1m'));
      expect(StatsFormatter.formatWatchTime(150), equals('2m'));
      expect(StatsFormatter.formatWatchTime(3600), equals('1h'));
      expect(StatsFormatter.formatWatchTime(3900), equals('1h 5m'));
      expect(StatsFormatter.formatWatchTime(7200), equals('2h'));
    });

    test('StatsFormatter should format numbers correctly', () {
      expect(StatsFormatter.formatNumber(500), equals('500'));
      expect(StatsFormatter.formatNumber(1000), equals('1K'));
      expect(StatsFormatter.formatNumber(1500), equals('1.5K'));
      expect(StatsFormatter.formatNumber(1000000), equals('1M'));
      expect(StatsFormatter.formatNumber(1500000), equals('1.5M'));
    });

    test('StatsFormatter should format percentages correctly', () {
      expect(StatsFormatter.formatPercentage(75.0), equals('75%'));
      expect(StatsFormatter.formatPercentage(78.5), equals('78.5%'));
      expect(StatsFormatter.formatPercentage(100.0), equals('100%'));
    });

    test('StatsFormatter should provide activity descriptions', () {
      expect(StatsFormatter.getActivityDescription(ActivityType.videoWatched), 
             equals('Watched video'));
      expect(StatsFormatter.getActivityDescription(ActivityType.contentCompleted), 
             equals('Completed content'));
      expect(StatsFormatter.getActivityDescription(ActivityType.testPassed), 
             equals('Passed test'));
      expect(StatsFormatter.getActivityDescription(ActivityType.login), 
             equals('Logged in'));
      expect(StatsFormatter.getActivityDescription(ActivityType.moduleCompleted), 
             equals('Completed module'));
      expect(StatsFormatter.getActivityDescription('unknown'), 
             equals('Activity'));
    });

    test('Activity types should be defined correctly', () {
      expect(ActivityType.videoWatched, equals('video_watched'));
      expect(ActivityType.contentCompleted, equals('content_completed'));
      expect(ActivityType.testPassed, equals('test_passed'));
      expect(ActivityType.login, equals('login'));
      expect(ActivityType.moduleCompleted, equals('module_completed'));
      expect(ActivityType.streakMaintained, equals('streak_maintained'));
    });

    test('Enhanced user stats service should be properly configured', () {
      // Test that the service class exists and has the expected methods
      expect(EnhancedUserStatsService.updateUserStats, isA<Function>());
      expect(EnhancedUserStatsService.getDetailedUserStats, isA<Function>());
      expect(EnhancedUserStatsService.recordVideoWatched, isA<Function>());
      expect(EnhancedUserStatsService.recordContentCompleted, isA<Function>());
      expect(EnhancedUserStatsService.recordTestPassed, isA<Function>());
      expect(EnhancedUserStatsService.recordLogin, isA<Function>());
    });

    test('UserStatsUpdateRequest should handle optional fields correctly', () {
      final minimalRequest = UserStatsUpdateRequest(
        activityType: ActivityType.login,
      );

      final json = minimalRequest.toJson();
      
      expect(json['activityType'], equals(ActivityType.login));
      expect(json.containsKey('contentId'), isFalse);
      expect(json.containsKey('contentType'), isFalse);
      expect(json.containsKey('timeSpent'), isFalse);
      expect(json.containsKey('score'), isFalse);
    });

    test('Achievement model should handle optional fields correctly', () {
      final achievement = Achievement(
        id: 'test-achievement',
        title: 'Test Achievement',
        unlockedAt: '2024-01-15T10:30:00Z',
      );

      expect(achievement.id, equals('test-achievement'));
      expect(achievement.title, equals('Test Achievement'));
      expect(achievement.description, isNull);
      expect(achievement.iconUrl, isNull);
      expect(achievement.unlockedAt, equals('2024-01-15T10:30:00Z'));
    });

    test('ModuleProgressStats should calculate correctly', () {
      final stats = ModuleProgressStats(
        completedContent: 7,
        totalContent: 10,
        progressPercentage: 70.0,
      );

      expect(stats.completedContent, equals(7));
      expect(stats.totalContent, equals(10));
      expect(stats.progressPercentage, equals(70.0));
    });

    test('RecentActivity should deserialize correctly', () {
      final activityJson = {
        'type': 'video_watched',
        'contentTitle': 'Advanced Flutter Concepts',
        'timestamp': '2024-01-15T14:30:00Z',
      };

      final activity = RecentActivity.fromJson(activityJson);
      
      expect(activity.type, equals('video_watched'));
      expect(activity.contentTitle, equals('Advanced Flutter Concepts'));
      expect(activity.timestamp, equals('2024-01-15T14:30:00Z'));
    });
  });
}
